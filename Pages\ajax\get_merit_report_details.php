<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

$response = ['success' => false, 'message' => ''];

if (!isset($_GET['report_id'])) {
    $response['message'] = 'Report ID is missing.';
    echo json_encode($response);
    exit;
}

$reportId = intval($_GET['report_id']);

try {
    $file = fopen(__DIR__ . "/../connection/one.txt", "r");
    if (!$file) {
        throw new Exception('Error reading settings file.');
    }
    
    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }

    $stmt = $conn->prepare("SELECT * FROM merit_reports WHERE id_merit_reports = ?");
    if ($stmt === false) {
        throw new Exception('Prepare failed: ' . $conn->error);
    }

    $stmt->bind_param("i", $reportId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $reportDetails = $result->fetch_assoc();
        $response['success'] = true;
        $response['data'] = $reportDetails;
    } else {
        $response['message'] = 'No merit report found with the given ID.';
    }

    $stmt->close();
    $conn->close();

} catch (Exception $e) {
    $response['message'] = 'System Error: ' . $e->getMessage();
}

echo json_encode($response);
?>
