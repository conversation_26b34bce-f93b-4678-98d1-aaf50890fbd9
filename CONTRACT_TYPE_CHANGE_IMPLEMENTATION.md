# Contract Type Change Implementation

## Overview
This implementation ensures that when a contract's type is changed (e.g., from daily open/closed to monthly open/closed, or vice versa), all associated achievement_reports records are automatically deleted.

## Implementation Details

### Modified File
- **File**: `Pages\create_contract_con.php`
- **Lines Modified**: 450-578 (approximately)

### Changes Made

1. **Added Contract Type Check Logic**:
   - Before updating a contract, the system now retrieves the current contract_type
   - Compares the current contract_type with the new contract_type
   - If they differ, deletes all related achievement_reports records

2. **Contract Type Filtering During Updates**:
   - When updating existing contracts, only equivalent contract types are shown as options
   - Filtering rules:
     - Monthly Open (1) ↔ Daily Open (3)
     - Monthly Closed (2) ↔ Daily Closed (4)
   - This ensures users can only switch between equivalent types (open ↔ open, closed ↔ closed)
   - All options are available when creating new contracts

3. **User Confirmation Dialog**:
   - JavaScript validation checks if contract type has changed during updates
   - Shows confirmation dialog warning about achievement_reports deletion
   - User must click "OK" to proceed with the operation
   - Operation is cancelled if user clicks "Cancel"

3. **Database Operations Added**:
   ```sql
   -- Get current contract type
   SELECT contract_type FROM contract WHERE id_contract = ?

   -- Delete related achievement reports if type changed
   DELETE FROM achievement_reports WHERE id_contract = ?

   -- Update contract (existing logic)
   UPDATE contract SET ... WHERE id_contract = ?
   ```

4. **Enhanced User Feedback**:
   - Success message now indicates when achievement_reports were deleted due to contract type change
   - Logging added for debugging purposes

### Code Flow

1. **Contract Loading**: When updating an existing contract:
   - Original contract_type is stored in JavaScript variable
   - Contract type options are filtered to show only equivalent alternatives
2. **Option Filtering**: Based on current contract type:
   - Monthly Open (1) → Shows Monthly Open (1) + Daily Open (3)
   - Monthly Closed (2) → Shows Monthly Closed (2) + Daily Closed (4)
   - Daily Open (3) → Shows Daily Open (3) + Monthly Open (1)
   - Daily Closed (4) → Shows Daily Closed (4) + Monthly Closed (2)
3. **User Interaction**: User can only select from filtered equivalent options
4. **Form Submission**: User clicks "Update" button
5. **Client-Side Validation**: JavaScript checks if contract type has changed
6. **Confirmation Dialog**: If type changed, show warning dialog about achievement_reports deletion
7. **User Decision**: User must confirm to proceed or cancel the operation
8. **Server-Side Processing**: If confirmed:
   - System fetches current contract_type from database
   - Compares current type with new type from form
   - If types differ, deletes all achievement_reports records with matching id_contract
   - Logs the deletion for audit purposes
9. **Contract Update**: Proceed with normal contract update
10. **User Notification**: Display appropriate success message

### Contract Types
The system uses the following contract type values:
- `1`: Monthly contract
- `2`: Daily contract

The frontend may use different values (1-4) but they are converted to 1 or 2 in the backend:
- Frontend values 1-2 → Backend value 1 (Monthly)
- Frontend values 3-4 → Backend value 2 (Daily)

### JavaScript Implementation Details

**Variables Added**:
- `originalContractType`: Stores the initial contract type when loading an existing contract

**Functions Added**:
- `filterContractTypeOptions(currentType)`: Filters dropdown to show only equivalent contract type alternatives
- `resetContractTypeOptions()`: Restores all contract type options for new contracts

**Functions Modified**:
- `validateForm()`: Enhanced to check for contract type changes and show confirmation dialog
- Contract loading logic: Stores original contract type and applies filtering when populating form for updates
- Form reset logic: Clears original contract type and resets all options when switching to new contract mode

**Confirmation Dialog**:
- Appears only when updating existing contracts and contract type has changed
- Uses native JavaScript `confirm()` function for maximum compatibility
- Returns `false` to prevent form submission if user cancels

### Error Handling
- Database connection errors are caught and displayed
- SQL preparation and execution errors are handled gracefully
- Transaction rollback is not implemented as the operations are separate
- JavaScript validation prevents form submission if user cancels confirmation

### Testing
Two test files have been created to verify the functionality:

1. **`test_contract_type_change.php`** - Server-side testing:
   - Tests contract type change scenarios
   - Verifies achievement_reports deletion
   - Confirms no deletion when type remains the same

2. **`test_contract_type_confirmation.html`** - Client-side testing:
   - Tests the JavaScript confirmation dialog
   - Simulates contract update scenarios
   - Verifies user interaction flow

3. **`test_contract_type_filtering.html`** - Contract type filtering testing:
   - Tests the contract type option filtering functionality
   - Verifies that only equivalent alternatives are shown
   - Interactive testing for all contract type combinations

## Database Schema Reference

### achievement_reports Table
```sql
CREATE TABLE achievement_reports (
    id_achievement_reports INT AUTO_INCREMENT PRIMARY KEY,
    id_Project INT,
    id_contract INT,
    id_extension_contract INT,
    id_permanent_diapers INT,
    start_date_achievement_reports DATETIME,
    end_date_achievement_reports DATETIME,
    data_todo_list_achievement JSON,
    actual_working_days INT,
    add_achievement_reports TIMESTAMP
);
```

### contract Table (relevant fields)
```sql
CREATE TABLE contract (
    id_contract INT AUTO_INCREMENT PRIMARY KEY,
    contract_type INT,
    -- other fields...
);
```

## Usage
This functionality is triggered when:
1. User accesses the contract update form (`Pages\create_contract_con.php`)
2. Selects an existing contract for update
3. **Contract Type Filtering**: The contract type dropdown automatically shows only equivalent alternatives:
   - If current type is "Monthly Open" → Shows "Monthly Open" + "Daily Open"
   - If current type is "Monthly Closed" → Shows "Monthly Closed" + "Daily Closed"
   - If current type is "Daily Open" → Shows "Daily Open" + "Monthly Open"
   - If current type is "Daily Closed" → Shows "Daily Closed" + "Monthly Closed"
4. User selects a different contract type (if desired)
5. Clicks the "Update" button
6. **Confirmation Dialog**: If type changed, a warning message appears stating:
   ```
   تحذير: تم تغيير نوع العقد!

   سيؤدي هذا التغيير إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.

   هل تريد المتابعة؟
   ```
7. User must click "OK" to proceed or "Cancel" to abort the operation
8. If confirmed, the deletion and update proceed automatically

**For New Contracts**: All contract type options are available without restrictions.

## Security Considerations
- Uses prepared statements to prevent SQL injection
- Validates contract existence before proceeding
- Logs operations for audit trail
- Proper error handling prevents information disclosure

## Future Enhancements
Consider implementing:
1. Backup of deleted achievement_reports before deletion
2. User confirmation dialog when contract type change will delete reports
3. Transaction wrapping for atomic operations
4. More detailed logging with timestamps and user information
