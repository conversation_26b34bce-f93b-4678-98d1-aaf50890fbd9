<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Get selected table and validate it
$valid_tables = [
    'contract' => 'العقود',
    'extension_contract' => 'تمديد العقود',
    'attendance_notes' => 'مذكرات الدوام',
    'achievement_reports' => 'تقارير الإنجاز',
    'merit_reports' => 'تقارير الاستحقاق'
];

$selected_table = isset($_GET['table']) && array_key_exists($_GET['table'], $valid_tables) 
    ? $_GET['table'] 
    : 'contract';

// Build query based on selected table
try {
    switch ($selected_table) {
        case 'contract':
            $filters = [];
            if (!empty($_GET['project_filter'])) {
                $filters[] = "c.id_Project = '" . $conn->real_escape_string($_GET['project_filter']) . "'";
            }
            if (!empty($_GET['employee_filter'])) {
                $filters[] = "e.id_employees = '" . $conn->real_escape_string($_GET['employee_filter']) . "'";
            }
            // Always exclude closed contracts
            $filters[] = "c.status_contract = 1";
            $whereSQL = count($filters) > 0 ? " WHERE " . implode(" AND ", $filters) : "";
            
            $query = "SELECT 
                     c.id_contract as 'رقم العقد',
                     c.id_Project as 'رقم المشروع',
                     e.name_ar_contract as 'الاسم',
                     c.name_Job as 'المسمى الوظيفي',
                     c.id_contract as 'رقم العقد',
                     CASE 
                         WHEN c.contract_type = 1 THEN 'أجر شهري'
                         WHEN c.contract_type = 2 THEN 'أجر يومي'
                         ELSE 'غير محدد'
                     END as 'نوع العقد',
                     e.Identity_number_contract as 'رقم الهوية',
                     c.start_date_contract as 'تاريخ البداية',
                     c.end_date_contract as 'تاريخ النهاية',
                     c.add_contract as 'تاريخ الإضافة'
                     FROM contract c
                     LEFT JOIN project p ON c.id_Project = p.id_Project
                     LEFT JOIN employees e ON c.id_employees = e.id_employees
                     " . $whereSQL . "
                     ORDER BY c.id_contract DESC";
            break;

        case 'extension_contract':
            $filters = [];
            if (!empty($_GET['project_filter'])) {
                $filters[] = "c.id_Project = '" . $conn->real_escape_string($_GET['project_filter']) . "'";
            }
            if (!empty($_GET['employee_filter'])) {
                $filters[] = "e.id_employees = '" . $conn->real_escape_string($_GET['employee_filter']) . "'";
            }
            // Always exclude closed contracts
            $filters[] = "c.status_contract = 1";
            $whereSQL = count($filters) > 0 ? " WHERE " . implode(" AND ", $filters) : "";
            
            $query = "SELECT 
                     ec.id_extension_contract as 'رقم التمديد',
                     c.id_Project as 'رقم المشروع',
                     e.name_ar_contract as 'الاسم',
                     c.name_Job as 'المسمى الوظيفي',
                     c.id_contract as 'رقم العقد',
                     CASE 
                         WHEN c.contract_type = 1 THEN 'أجر شهري'
                         WHEN c.contract_type = 2 THEN 'أجر يومي'
                         ELSE 'غير محدد'
                     END as 'نوع العقد',
                     e.Identity_number_contract as 'رقم الهوية',
                     ec.start_date_contract as 'تاريخ البداية',
                     ec.end_date_contract as 'تاريخ النهاية',
                     ec.add_extension_contract as 'تاريخ الإضافة'
                     FROM extension_contract ec
                     LEFT JOIN contract c ON ec.id_contract = c.id_contract
                     LEFT JOIN project p ON c.id_Project = p.id_Project
                     LEFT JOIN employees e ON c.id_employees = e.id_employees
                     " . $whereSQL . "
                     ORDER BY ec.id_extension_contract DESC";
            break;

        case 'attendance_notes':
            $filters = [];
            if (!empty($_GET['project_filter'])) {
                $filters[] = "pd.id_Project = '" . $conn->real_escape_string($_GET['project_filter']) . "'";
            }
            if (!empty($_GET['employee_filter'])) {
                $filters[] = "e.id_employees = '" . $conn->real_escape_string($_GET['employee_filter']) . "'";
            }
            // Always exclude closed contracts
            $filters[] = "c.status_contract = 1";
            $whereSQL = count($filters) > 0 ? " WHERE " . implode(" AND ", $filters) : "";
            
            $query = "SELECT 
                     pd.id_permanent_diapers as 'رقم المذكرة',
                     pd.id_Project as 'رقم المشروع',
                     e.name_ar_contract as 'الاسم',
                     c.name_Job as 'المسمى الوظيفي',
                     c.id_contract as 'رقم العقد',
                     CASE 
                         WHEN c.contract_type = 1 THEN 'أجر شهري'
                         WHEN c.contract_type = 2 THEN 'أجر يومي'
                         ELSE 'غير محدد'
                     END as 'نوع العقد',
                     pd.start_date_permanent_diapers as 'تاريخ البداية',
                     pd.end_date_permanent_diapers as 'تاريخ النهاية',
                     pd.add_permanent_diapers as 'تاريخ الإضافة'
                     FROM permanent_diapers pd
                     LEFT JOIN project p ON pd.id_Project = p.id_Project
                     LEFT JOIN contract c ON pd.id_contract = c.id_contract
                     LEFT JOIN employees e ON c.id_employees = e.id_employees
                     " . $whereSQL . "
                     ORDER BY pd.id_permanent_diapers DESC";
            break;

        case 'achievement_reports':
            $filters = [];
            if (!empty($_GET['project_filter'])) {
                $filters[] = "ar.id_Project = '" . $conn->real_escape_string($_GET['project_filter']) . "'";
            }
            if (!empty($_GET['employee_filter'])) {
                $filters[] = "e.id_employees = '" . $conn->real_escape_string($_GET['employee_filter']) . "'";
            }
            // Always exclude closed contracts
            $filters[] = "c.status_contract = 1";
            $whereSQL = count($filters) > 0 ? " WHERE " . implode(" AND ", $filters) : "";
            
            $query = "SELECT 
                     ar.id_achievement_reports as 'رقم التقرير',
                     ar.id_Project as 'رقم المشروع',
                     e.name_ar_contract as 'الاسم',
                     c.name_Job as 'المسمى الوظيفي',
                     c.id_contract as 'رقم العقد',
                     CASE 
                         WHEN c.contract_type = 1 THEN 'أجر شهري'
                         WHEN c.contract_type = 2 THEN 'أجر يومي'
                         ELSE 'غير محدد'
                     END as 'نوع العقد',
                     e.Identity_number_contract as 'رقم الهوية',
                     ar.start_date_achievement_reports as 'تاريخ البداية',
                     ar.end_date_achievement_reports as 'تاريخ النهاية',
                     ar.actual_working_days as 'أيام العمل الفعلية'
                     FROM achievement_reports ar
                     LEFT JOIN project p ON ar.id_Project = p.id_Project
                     LEFT JOIN contract c ON ar.id_contract = c.id_contract
                     LEFT JOIN employees e ON c.id_employees = e.id_employees
                     " . $whereSQL . "
                     ORDER BY ar.id_achievement_reports DESC";
            break;

        case 'merit_reports':
            $filters = [];
            if (!empty($_GET['project_filter'])) {
                $filters[] = "mr.id_Project = '" . $conn->real_escape_string($_GET['project_filter']) . "'";
            }
            if (!empty($_GET['employee_filter'])) {
                $filters[] = "e.id_employees = '" . $conn->real_escape_string($_GET['employee_filter']) . "'";
            }

            // Date filters - flexible date range filtering
            if (!empty($_GET['date_from'])) {
                $filters[] = "ar.start_date_achievement_reports >= '" . $conn->real_escape_string($_GET['date_from']) . "'";
            }
            if (!empty($_GET['date_to'])) {
                $filters[] = "ar.end_date_achievement_reports <= '" . $conn->real_escape_string($_GET['date_to']) . " 23:59:59'";
            }

            // Always exclude closed contracts
            $filters[] = "c.status_contract = 1";
            $whereSQL = count($filters) > 0 ? " WHERE " . implode(" AND ", $filters) : "";

            $query = "SELECT
                     mr.id_merit_reports as 'رقم التقرير',
                     mr.id_Project as 'رقم المشروع',
                     e.name_ar_contract as 'اسم الموظف',
                     c.id_contract as 'رقم العقد',
                     CASE
                         WHEN c.contract_type = 1 THEN 'أجر شهري'
                         WHEN c.contract_type = 2 THEN 'أجر يومي'
                         ELSE 'غير محدد'
                     END as 'نوع العقد',
                     c.name_Job as 'المسمى الوظيفي',
                     ar.start_date_achievement_reports as 'تاريخ بداية الفترة',
                     ar.end_date_achievement_reports as 'تاريخ نهاية الفترة',
                     mr.today_wage as 'الأجر اليومي',
                     mr.actual_working_days as 'أيام العمل الفعلية',
                     mr.tax_rate as 'نسبة الضريبة',
                     mr.Insurance as 'التأمين',
                     mr.predecessor as 'السلف',
                     mr.total as 'إجمالي قبل الخصم',
                     mr.total_after_discount as 'الإجمالي بعد الخصم'
                     FROM merit_reports mr
                     LEFT JOIN project p ON mr.id_Project = p.id_Project
                     LEFT JOIN contract c ON mr.id_contract = c.id_contract
                     LEFT JOIN employees e ON c.id_employees = e.id_employees
                     LEFT JOIN achievement_reports ar ON mr.id_achievement_reports = ar.id_achievement_reports
                     " . $whereSQL . "
                     ORDER BY ar.start_date_achievement_reports DESC, mr.id_merit_reports DESC";
            break;
    }

    $result = $conn->query($query);
    if (!$result) {
        throw new Exception($conn->error);
    }
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

// Fetch filter options for dropdowns
$admins_query = "SELECT id_administrators, full_name_ar FROM administrators ORDER BY full_name_ar";
$areas_query = "SELECT id_target_areas, name_ar FROM target_areas ORDER BY name_ar";
$orgs_query = "SELECT id_implementing_organizations, name_ar FROM implementing_organizations ORDER BY name_ar";
$funders_query = "SELECT id_funders, name_ar FROM funders ORDER BY name_ar";

$admins = $conn->query($admins_query);
$areas = $conn->query($areas_query);
$orgs = $conn->query($orgs_query);
$funders = $conn->query($funders_query);

// Column name translations
$column_translations = [
    // Project columns
    'id_Project' => 'الرقم',
    'Project_name' => 'اسم المشروع',
    'Project_name_en' => 'اسم المشروع (إنجليزي)',
    'Project_status' => 'حالة المشروع',
    'Project_start_date' => 'تاريخ البداية',
    'Project_end_date' => 'تاريخ النهاية',
    'Project_description' => 'وصف المشروع',
    'Project_description_en' => 'وصف المشروع (إنجليزي)',
    'status_text' => 'الحالة',
    'add' => 'تاريخ الإضافة',
    
    // Contract columns
    'id_contract' => 'رقم العقد',
    'name_ar_contract' => 'اسم العقد',
    'name_en_contract' => 'اسم العقد (إنجليزي)',
    'contract_type' => 'نوع العقد',
    'contract_type_text' => 'نوع العقد',
    'version_date' => 'تاريخ الإصدار',
    'extension' => 'عدد التمديدات',
    'start_date_contract' => 'تاريخ بداية العقد',
    'end_date_contract' => 'تاريخ نهاية العقد',
    'status_contract' => 'حالة العقد',
    'wage_contract' => 'الأجر',
    'name_Job' => 'المسمى الوظيفي',
    'Identity_contract_ar' => 'نوع الهوية',
    'Identity_contract_en' => 'نوع الهوية (إنجليزي)',
    'Identity_number_contract' => 'رقم الهوية',
    'Identity_issue_contract_ar' => 'جهة الإصدار',
    'Identity_issue_contract_en' => 'جهة الإصدار (إنجليزي)',
    'Identity_issue_date_contract' => 'تاريخ الإصدار',
    'amount_written_ar' => 'المبلغ كتابة',
    'amount_written_en' => 'المبلغ كتابة (إنجليزي)',
    'add_contract' => 'تاريخ الإضافة',
    
    // Extension contract columns
    'id_extension_contract' => 'رقم التمديد',
    'add_extension_contract' => 'تاريخ الإضافة',
    
    // Permanent diapers columns
    'id_permanent_diapers' => 'رقم المذكرة',
    'start_date_permanent_diapers' => 'تاريخ البداية',
    'end_date_permanent_diapers' => 'تاريخ النهاية',
    'add_permanent_diapers' => 'تاريخ الإضافة',
    
    // Achievement reports columns
    'id_achievement_reports' => 'رقم التقرير',
    'start_date_achievement_reports' => 'تاريخ البداية',
    'end_date_achievement_reports' => 'تاريخ النهاية',
    'تاريخ بداية الفترة' => 'تاريخ بداية الفترة',
    'تاريخ نهاية الفترة' => 'تاريخ نهاية الفترة',
    'actual_working_days' => 'أيام العمل الفعلية',
    'add_achievement_reports' => 'تاريخ الإضافة',
    
    // Merit reports columns
    'id_merit_reports' => 'رقم الاستحقاق',
    'today_wage' => 'الأجر اليومي',
    'total' => 'إجمالي الاستحقاق',
    
    // Job titles columns
    'id_Job_titles' => 'رقم المسمى الوظيفي',
    'name_Job_en' => 'المسمى الوظيفي (إنجليزي)',
    'data_todo_list_Job' => 'قائمة المهام'
];

// Function to get formatted value
function getFormattedValue($key, $value) {
    global $column_translations;
    
    // Skip empty values
    if ($value === null || $value === '') {
        return '<span class="text-muted">-</span>';
    }
    
    // Format based on column type
    if (strpos($key, 'status') !== false) {
        $status_class = $value == 'نشط' ? 'bg-success' : 'bg-warning';
        return "<span class='badge {$status_class}'>" . htmlspecialchars($value) . "</span>";
    } 
    
    // Updated condition to also check for the Arabic word for date
    if (strpos($key, 'date') !== false || strpos($key, 'add') !== false || strpos($key, 'تاريخ') !== false) {
        return date('Y-m-d', strtotime($value));
    }
    
    if (strpos($key, 'wage') !== false || $key === 'total') {
        return number_format($value, 2) . ' ريال';
    }
    
    return htmlspecialchars($value);
}

// Function to get column class based on key
function getColumnClass($key) {
    if (strpos($key, 'id_') === 0) {
        return 'col-id';
    }
    if (strpos($key, '_name') !== false || strpos($key, 'name_') !== false) {
        if (strpos($key, '_en') !== false) {
            return 'col-name-en';
        }
        return 'col-name';
    }
    if (strpos($key, 'description') !== false) {
        return 'col-description';
    }
    if (strpos($key, 'date') !== false || strpos($key, 'add') !== false) {
        return 'col-date';
    }
    if (strpos($key, 'status') !== false) {
        return 'col-status';
    }
    if (strpos($key, 'wage') !== false || $key === 'total' || strpos($key, 'amount') !== false) {
        return 'col-currency';
    }
    if ($key === 'achievement_rate') {
        return 'col-progress';
    }
    if (strpos($key, 'extension') !== false || strpos($key, 'actual_working_days') !== false) {
        return 'col-number';
    }
    return 'col-text';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظرة عامة على قاعدة البيانات - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../assets/lib/datatables/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        .table-container {
            background-color: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            margin: 1rem 0;
            padding: 0;
            border: 1px solid var(--border-color);
            overflow: hidden;
        }
        
        /* Table Filters */
        .table-filters {
            background: var(--bg-main);
            border-bottom: 1px solid var(--border-color);
        }
        
        .quick-filters {
            padding: 1rem;
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
        }
        
        .quick-filter-item {
            min-width: 150px;
            position: relative;
        }
        
        .quick-filter-item .form-select {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0;
        }
        
        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            margin-top: 1rem;
            border-top: 1px solid var(--border-color);
        }
        
        /* Dark theme adjustments for filters */
        [data-theme="dark"] .table-filters {
            background: var(--bg-main);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }
        
        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }
        
        .table-scroll-container {
            height: calc(100vh - 300px);
            min-height: 400px;
            overflow: auto;
            position: relative;
        }
        
        .table {
            margin-bottom: 0;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table th {
            position: sticky;
            top: 0;
            z-index: 2;
            background-color: var(--bg-main) !important;
            border-bottom: 2px solid var(--border-color);
            text-align: center;
        }
        
        .table th,
        .table td {
            border: 1px solid var(--border-color);
            white-space: normal;
            vertical-align: middle;
            min-width: 120px; /* Base minimum width for all columns */
            background-color: transparent !important;
        }
        
        .table tbody tr:hover td {
            background-color: var(--hover-color) !important;
        }
        
        .table tbody tr td {
            background-color: var(--bg-card) !important;
        }
        
        /* Dark theme specific adjustments */
        [data-theme="dark"] .table {
            border-color: var(--border-color);
            color: white;
        }
        
        [data-theme="dark"] .table th,
        [data-theme="dark"] .table td {
            border-color: var(--border-color);
            color: white;
        }
        
        [data-theme="dark"] .table th {
            background-color: var(--bg-main) !important;
            border-bottom: 2px solid var(--border-color);
            color: white;
        }
        
        [data-theme="dark"] .table tbody tr td {
            background-color: var(--bg-card) !important;
            color: white;
        }
        
        [data-theme="dark"] .table tbody tr:hover td {
            background-color: var(--hover-color) !important;
            color: white;
        }
        
        [data-theme="dark"] .text-muted {
            color: rgba(255, 255, 255, 0.6) !important;
        }
        
        [data-theme="dark"] .dataTables_info,
        [data-theme="dark"] .dataTables_length label,
        [data-theme="dark"] .dataTables_filter label {
            color: white;
        }
        
        [data-theme="dark"] .dataTables_length select,
        [data-theme="dark"] .dataTables_filter input {
            color: white;
            background-color: var(--bg-card);
        }
        
        [data-theme="dark"] .paginate_button {
            color: white !important;
        }
        
        [data-theme="dark"] .paginate_button.disabled {
            color: rgba(255, 255, 255, 0.4) !important;
        }
        
        [data-theme="dark"] .paginate_button:not(.current):hover {
            color: white !important;
        }
        
        /* DataTable wrapper modifications */
        .dataTables_wrapper {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            width: 100%;
        }
        
        .dataTables_wrapper .row:first-child,
        .dataTables_wrapper .row:last-child {
            margin: 0;
            flex-shrink: 0;
            background-color: var(--bg-card);
            z-index: 1;
            padding: 0.5rem 0;
        }
        
        .dataTables_wrapper .row:nth-child(2) {
            margin: 0;
            overflow-x: auto;
            min-height: 0;
            width: 100%;
        }
        
        .dataTables_scroll {
            overflow-x: auto;
            margin: 0;
            width: 100%;
        }
        
        .nav-pills .nav-link {
            color: var(--text-color);
            border-radius: 10px;
            padding: 0.8rem 1.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .nav-pills .nav-link:hover {
            background-color: var(--hover-color);
        }
        
        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .table-scroll {
            overflow-x: auto;
            margin: 0 -1.5rem;
            padding: 0 1.5rem;
        }
        
        .table-scroll::-webkit-scrollbar {
            height: 6px;
        }
        
        .table-scroll::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }
        
        .table-scroll::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }
        
        .badge {
            font-size: 0.85rem;
            padding: 0.5em 0.85em;
            border-radius: 6px;
        }
        
        .table-info-card {
            background-color: var(--bg-card);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
        }
        
        .table-info-card h6 {
            color: var(--text-muted);
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .table-info-card p {
            color: var(--text-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0;
        }
        
        .nav-pills-wrapper {
            overflow-x: auto;
            margin: 0 -1rem;
            padding: 0 1rem;
        }
        
        .nav-pills-wrapper::-webkit-scrollbar {
            height: 6px;
        }
        
        .nav-pills-wrapper::-webkit-scrollbar-track {
            background: var(--bg-main);
            border-radius: 3px;
        }
        
        .nav-pills-wrapper::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }
        
        .nav-pills {
            flex-wrap: nowrap;
            min-width: max-content;
        }
        
        .dataTables_wrapper .row {
            margin: 0;
            align-items: center;
        }
        
        .dataTables_wrapper .row:not(:last-child) {
            margin-bottom: 1rem;
        }
        
        .dataTables_filter {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        
        .dataTables_filter label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            color: var(--text-color);
            font-weight: 500;
            width: 100%;
        }
        
        .dataTables_filter input {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 8px;
            padding: 0.6rem 1rem;
            font-size: 0.95rem;
            width: 300px;
            transition: all 0.3s ease;
        }
        
        .dataTables_length {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        .dataTables_length label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            color: var(--text-color);
            font-weight: 500;
        }
        
        .dataTables_length select {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 8px;
            padding: 0.6rem 2rem 0.6rem 1rem;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }
        
        .dataTables_paginate {
            display: inline-flex;
            justify-content: flex-end;
            align-items: center;
            gap: 0.25rem;
            margin-top: 1.5rem;
            padding: 0.5rem 0;
        }
        
        .dataTables_paginate .paginate_button {
            min-width: 32px;
            height: 32px;
            padding: 0;
            margin: 0 1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-card) !important;
            color: var(--text-color) !important;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .dataTables_paginate .paginate_button.current {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: white !important;
            font-weight: 600;
        }
        
        .dataTables_paginate .paginate_button:hover:not(.current):not(.disabled) {
            background-color: var(--hover-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--text-color) !important;
        }
        
        .dataTables_info {
            color: var(--text-color);
            font-size: 0.9rem;
            padding-top: 0.85rem;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .dataTables_length,
            .dataTables_filter {
                justify-content: flex-start;
                margin-bottom: 1rem;
            }
            
            .dataTables_filter input {
                width: 100%;
            }
            
            .dataTables_paginate {
                justify-content: flex-start;
                flex-wrap: wrap;
            }
            
            .dataTables_info {
                text-align: right;
                margin-bottom: 0.5rem;
            }
            
            .table-info-card {
                margin-bottom: 0.5rem;
            }
        }
        
        .progress {
            background-color: var(--bg-main);
            border-radius: 6px;
            overflow: hidden;
        }
        
        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.85rem;
            font-weight: 600;
            transition: width 0.3s ease;
        }
        
        .text-muted {
            color: var(--text-muted) !important;
        }
        
        /* Column width classes */
        .col-id {
            width: 60px;
            min-width: 60px;
            max-width: 60px;
        }
        
        .col-name {
            width: 200px;
            min-width: 200px;
            max-width: 200px;
        }
        
        .col-name-en {
            width: 200px;
            min-width: 200px;
            max-width: 200px;
        }
        
        .col-description {
            width: 300px;
            min-width: 300px;
            max-width: 300px;
        }
        
        .col-date {
            width: 150px;
            min-width: 150px;
            max-width: 150px;
        }
        
        .col-status {
            width: 80px;
            min-width: 80px;
            max-width: 80px;
        }
        
        .col-number {
            width: 120px;
            min-width: 120px;
            max-width: 120px;
        }
        
        .col-progress {
            width: 150px;
            min-width: 150px;
            max-width: 150px;
        }
        
        .col-currency {
            width: 150px;
            min-width: 150px;
            max-width: 150px;
        }
        
        .col-text {
            width: 180px;
            min-width: 180px;
            max-width: 180px;
        }
        
        .table td {
            padding: 1rem;
            line-height: 1.5;
            height: auto;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* Table header styles */
        .table th {
            text-align: center !important;
            vertical-align: middle !important;
            white-space: nowrap;
            padding: 0.85rem 0.5rem;
            font-weight: 600;
            font-size: 0.85rem;
            background-color: var(--bg-main) !important;
            border-bottom: 2px solid var(--border-color);
        }
        
        /* Status badge styles */
        .badge {
            min-width: 65px;
            display: inline-block;
            text-align: center;
            padding: 0.4rem 0.6rem;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        /* Filter Form Styles */
        .filter-form {
            background-color: var(--bg-card);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .filter-form .form-label {
            color: var(--text-color);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .filter-form .form-control,
        .filter-form .form-select {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 6px;
            padding: 0.6rem 1rem;
            transition: all 0.3s ease;
        }
        
        .filter-form .form-control:focus,
        .filter-form .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }
        
        .filter-form .btn {
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            border-radius: 6px;
        }
        
        .filter-form .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .filter-form .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
        }
        
        .filter-form .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .filter-form .btn-secondary:hover {
            background-color: var(--hover-color);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .filter-form {
            background-color: var(--bg-card);
        }
        
        [data-theme="dark"] .filter-form .form-control,
        [data-theme="dark"] .filter-form .form-select {
            background-color: var(--bg-input);
            color: var(--text-color);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .filter-form .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
        }
        
        [data-theme="dark"] .filter-form .btn-secondary:hover {
            background-color: var(--hover-color);
        }
        
        /* Adjust table column widths for project view */
        .project-view .col-id {
            width: 80px;
            min-width: 80px;
        }
        
        .project-view .col-name {
            width: 250px;
            min-width: 250px;
        }
        
        .project-view .col-date {
            width: 150px;
            min-width: 150px;
        }
        
        .project-view .col-description {
            width: 300px;
            min-width: 300px;
        }
        
        .project-view .col-status {
            width: 120px;
            min-width: 120px;
        }
        
        /* Modern Filter Bar Styles */
        .filter-bar {
            background: var(--bg-card);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        
        .quick-filters {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-card);
        }
        
        .quick-filter-item {
            min-width: 150px;
        }
        
        .quick-filter-item .form-select {
            border-radius: 20px;
            padding: 0.4rem 2rem 0.4rem 1rem;
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }
        
        .quick-filter-item .form-select:hover {
            border-color: var(--primary-color);
        }
        
        .quick-filter-item .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }
        
        .advanced-filters-container {
            padding: 1.5rem;
            background: var(--bg-main);
            border-radius: 0 0 10px 10px;
        }
        
        .filter-group {
            background: var(--bg-card);
            border-radius: 8px;
            padding: 1rem;
            height: 100%;
        }
        
        .filter-group-title {
            color: var(--text-color);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .filter-group-title i {
            color: var(--primary-color);
        }
        
        .filter-group .input-group {
            gap: 0.5rem;
        }
        
        .filter-group .form-control,
        .filter-group .form-select {
            border: 1px solid var(--border-color);
            background-color: var(--bg-input);
            color: var(--text-color);
            border-radius: 6px;
            font-size: 0.875rem;
            padding: 0.4rem 0.75rem;
        }
        
        .filter-group .form-control:focus,
        .filter-group .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }
        
        .filter-actions {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-outline-secondary {
            color: var(--text-muted);
            border-color: var(--border-color);
            background: transparent;
            border-radius: 20px;
            padding: 0.4rem 1rem;
            font-size: 0.875rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
        }
        
        .btn-outline-secondary:hover {
            background: var(--hover-color);
            color: var(--text-color);
        }
        
        /* Dark theme adjustments */
        [data-theme="dark"] .filter-bar {
            background: var(--bg-card);
        }
        
        [data-theme="dark"] .quick-filters {
            background: var(--bg-card);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .advanced-filters-container {
            background: var(--bg-main);
        }
        
        [data-theme="dark"] .filter-group {
            background: var(--bg-card);
        }
        
        [data-theme="dark"] .filter-group-title {
            color: var(--text-color);
        }
        
        [data-theme="dark"] .form-control,
        [data-theme="dark"] .form-select {
            background-color: var(--bg-input);
            color: var(--text-color);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .btn-outline-secondary {
            color: var(--text-muted);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .btn-outline-secondary:hover {
            background: var(--hover-color);
            color: var(--text-color);
        }
        
        /* Date input styling */
        .form-control[type="date"] {
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 6px;
            padding: 0.6rem 1rem;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .form-control[type="date"]:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem var(--primary-light);
        }

        .form-control[type="date"]::-webkit-calendar-picker-indicator {
            background-color: var(--primary-color);
            border-radius: 3px;
            cursor: pointer;
        }

        [data-theme="dark"] .form-control[type="date"] {
            background-color: var(--bg-input);
            color: var(--text-color);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .form-control[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .quick-filter-item {
                min-width: 120px;
            }

            .filter-group {
                margin-bottom: 1rem;
            }

            .advanced-filters-container {
                padding: 1rem;
            }

            .filter-actions {
                justify-content: center;
            }
        }

        /* Refined table styles for merit reports */
        .table.merit-reports-table {
            font-size: 0.85rem;
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }
        
        .table.merit-reports-table th,
        .table.merit-reports-table td {
            overflow: hidden;
            text-overflow: ellipsis;
            border: 1px solid var(--border-color);
        }
        
        .table.merit-reports-table th {
            font-size: 0.8rem;
            padding: 0.65rem 0.5rem;
            background-color: var(--bg-card) !important;
            color: var(--text-color);
            font-weight: 600;
        }
        
        .table.merit-reports-table td {
            padding: 0.6rem 0.75rem;
            line-height: 1.4;
            vertical-align: middle;
        }
        
        /* Fixed column widths for merit reports */
        .table.merit-reports-table .col-project {
            width: 60px;
        }
        
        .table.merit-reports-table .col-employee {
            width: 220px;
        }
        
        .table.merit-reports-table .col-job {
            width: 150px;
        }
        
        .table.merit-reports-table .col-daily-wage {
            width: 100px;
        }
        
        .table.merit-reports-table .col-working-days {
            width: 120px;
        }
        
        .table.merit-reports-table .col-tax {
            width: 100px;
        }
        
        .table.merit-reports-table .col-insurance {
            width: 120px;
        }
        
        .table.merit-reports-table .col-advance {
            width: 100px;
        }
        
        .table.merit-reports-table .col-total,
        .table.merit-reports-table .col-net-total {
            width: 140px;
            font-weight: 600;
        }
        
        /* Add special styling for the totals */
        .table.merit-reports-table .col-total,
        .table.merit-reports-table .col-net-total {
            background-color: rgba(0,0,0,0.02) !important;
        }
        
        [data-theme="dark"] .table.merit-reports-table .col-total,
        [data-theme="dark"] .table.merit-reports-table .col-net-total {
            background-color: rgba(255,255,255,0.03) !important;
        }
        
        .table.merit-reports-table .employee-name {
            font-weight: 600;
            font-size: 1rem;
            color: var(--primary-color);
        }
        
        .table.merit-reports-table .job-title {
            font-style: italic;
            color: var(--text-muted);
            font-size: 0.8rem;
            margin-top: 0.2rem;
        }
        
        .table.merit-reports-table .financial-section {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px dashed var(--border-color);
        }
        
        .table.merit-reports-table .deductions-section {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px dashed var(--border-color);
            color: var(--text-muted);
            font-size: 0.8rem;
        }
        
        .table.merit-reports-table .total-section {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid var(--border-color);
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .table.merit-reports-table .currency {
            font-family: monospace;
            background-color: rgba(0,0,0,0.03);
            padding: 0.1rem 0.3rem;
            border-radius: 4px;
            font-size: 0.85rem;
        }
        
        [data-theme="dark"] .table.merit-reports-table .currency {
            background-color: rgba(255,255,255,0.07);
        }
        
        /* Summary row styles */
        .table-summary-row {
            background-color: var(--bg-main) !important;
            border-top: 2px solid var(--primary-color);
        }
        
        .table-summary-row td {
            background-color: var(--bg-main) !important;
            font-weight: bold;
        }
        
        [data-theme="dark"] .table-summary-row {
            background-color: var(--bg-card) !important;
        }
        
        [data-theme="dark"] .table-summary-row td {
            background-color: var(--bg-card) !important;
            color: white;
        }
        
        /* Styles for searchable project dropdown */
        .select2-container--bootstrap-5 {
            width: 100% !important;
        }
        
        /* Enhanced select field styling */
        .form-select.select2-search {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            padding: 0.625rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .form-select.select2-search:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
            outline: none;
        }
        
        .form-select.select2-search:hover {
            border-color: #bdbdbd;
        }
        
        /* Select2 customization */
        .select2-container--default .select2-selection--single {
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background-color: #fff;
            transition: all 0.3s ease;
        }
        
        .select2-container--default .select2-selection--single:hover {
            border-color: #bdbdbd;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
            padding-right: 1rem;
            padding-left: 1rem;
            color: #2d3748;
        }
        
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 46px;
            width: 30px;
        }
        
        .select2-dropdown {
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .select2-search__field {
            border-radius: 8px !important;
            padding: 0.5rem !important;
        }
        
        .select2-results__option {
            padding: 0.75rem 1rem;
        }
        
        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: rgba(var(--primary-rgb), 0.1);
        }
        
        /* RTL fixes */
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px !important;
            display: flex !important;
            align-items: center !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            width: 100% !important;
            padding-right: 8px !important;
            padding-left: 20px !important;
            display: block !important;
            position: static !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            white-space: nowrap !important;
            color: var(--select-text) !important;
        }
        
        /* Placeholder color */
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__placeholder {
            color: var(--select-placeholder) !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            position: absolute !important;
            left: 3px !important;
            right: auto !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow b {
            border-color: var(--select-text) transparent transparent transparent !important;
        }
        
        .select2-container--bootstrap-5.select2-container--open .select2-selection--single .select2-selection__arrow b {
            border-color: transparent transparent var(--select-text) transparent !important;
        }
        
        /* Dropdown styles */
        .select2-container--bootstrap-5 .select2-dropdown {
            background-color: var(--select-bg) !important;
            border: 1px solid var(--select-border) !important;
            border-radius: 0.375rem !important;
            text-align: right !important;
        }
        
        .select2-container--bootstrap-5 .select2-results__option {
            padding: 6px 12px !important;
            text-align: right !important;
            color: var(--select-text) !important;
        }
        
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        .select2-container--bootstrap-5 .select2-results__option[aria-selected=true] {
            background-color: var(--primary-color) !important;
            color: var(--text-light) !important;
        }
        
        /* Focus state */
        .select2-container--bootstrap-5.select2-container--focus .select2-selection,
        .select2-container--bootstrap-5.select2-container--open .select2-selection {
            border-color: var(--select-focus-border) !important;
            box-shadow: 0 0 0 0.25rem var(--select-focus-shadow) !important;
        }
        
        /* Search field in dropdown */
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field {
            border: 1px solid var(--select-border) !important;
            background-color: var(--select-bg) !important;
            color: var(--select-text) !important;
            border-radius: 0.375rem !important;
            padding: 0.375rem 0.75rem !important;
        }
        
        .select2-container--bootstrap-5 .select2-search--dropdown .select2-search__field:focus {
            border-color: var(--select-focus-border) !important;
            outline: none !important;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--border-color);
            color: var(--text-color);
        }
        
        [data-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
            color: var(--text-color);
        }
        
        [data-theme="dark"] .select2-dropdown {
            background-color: var(--bg-card);
            border-color: var(--border-color);
        }
        
        [data-theme="dark"] .select2-container--default .select2-results__option {
            color: var(--text-color);
        }
        
        [data-theme="dark"] .select2-search__field {
            background-color: var(--bg-input) !important;
            color: var(--text-color) !important;
            border-color: var(--border-color) !important;
        }
        
        /* Project filter specific styles */
        :root {
            --select-bg: #fff;
            --select-text: #495057;
            --select-border: #ced4da;
            --select-placeholder: #999;
            --select-hover-bg: #f8f9fa;
            --select-focus-border: #86b7fe;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }
        
        [data-theme="dark"] {
            --select-bg: #2b3035;
            --select-text: #e9ecef;
            --select-border: #495057;
            --select-placeholder: #6c757d;
            --select-hover-bg: #343a40;
            --select-focus-border: #0d6efd;
            --select-focus-shadow: rgba(13, 110, 253, 0.25);
        }
        
        /* Select2 customization */
        .select2-container--bootstrap-5 .select2-results > .select2-results__options {
            max-height: 250px;
            overflow-y: auto;
        }

        /* Add these styles to the existing CSS section */
        .table.merit-reports-table th {
            font-size: 0.7rem;
            padding: 0.4rem 0.5rem;
            background-color: var(--bg-card) !important;
            color: var(--text-color);
            font-weight: 600;
        }

        .table.merit-reports-table td {
            padding: 0.4rem 0.5rem;
            line-height: 1.3;
            vertical-align: middle;
        }

        /* Fixed column widths for contract table */
        .table.merit-reports-table .col-contract-id {
            width: 80px;
            min-width: 80px;
        }

        .table.merit-reports-table .col-project {
            width: 80px;
            min-width: 80px;
        }

        .table.merit-reports-table .col-name {
            width: 180px;
            min-width: 180px;
        }

        .table.merit-reports-table .col-job {
            width: 150px;
            min-width: 150px;
        }

        .table.merit-reports-table .col-type {
            width: 100px;
            min-width: 100px;
        }

        .table.merit-reports-table .col-id {
            width: 120px;
            min-width: 120px;
        }

        .table.merit-reports-table .col-date {
            width: 100px;
            min-width: 100px;
        }

        /* Add these styles to the existing CSS section */
        .table.merit-reports-table .col-extension-id {
            width: 80px;
            min-width: 80px;
        }

        .table.merit-reports-table .col-contract {
            width: 80px;
            min-width: 80px;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    $jsonFile = __DIR__ . '/../config/sidebar.json';
    $jsonContent = file_get_contents($jsonFile);
    if ($jsonContent === false) {
        die('Error: Unable to read sidebar configuration file');
    }
    $config = json_decode($jsonContent, true);
    if ($config === null) {
        die('Error: Invalid JSON in sidebar configuration file');
    }
    $currentPage = basename($_SERVER['PHP_SELF']);
    ?>
    
    <!-- Sidebar -->
    <div id="sidebar">
        <div class="logo">
            <div><?php echo isset($config['logo']) ? htmlspecialchars($config['logo']) : 'نظام الموارد البشرية'; ?></div>
        </div>
        
        <nav class="nav flex-column">
            <?php foreach ($config['menu_items'] as $item): ?>
                <?php if (isset($item['type']) && $item['type'] === 'dropdown'): ?>
                    <!-- Dropdown Section -->
                    <div class="nav-section">
                        <button class="nav-link section-toggle" data-section="<?php echo $item['id']; ?>">
                            <i class="bi <?php echo htmlspecialchars($item['icon']); ?>"></i>
                            <?php echo htmlspecialchars($item['title']); ?>
                        </button>
                        <div class="section-items" id="<?php echo $item['id']; ?>-section">
                            <?php foreach ($item['items'] as $subItem): ?>
                                <a href="<?php echo htmlspecialchars($subItem['url']); ?>" 
                                   class="nav-link sub-item <?php echo $currentPage === basename($subItem['url']) ? 'active' : ''; ?>">
                                    <i class="bi <?php echo htmlspecialchars($subItem['icon']); ?>"></i>
                                    <?php echo htmlspecialchars($subItem['title']); ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Single Link -->
                    <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                       class="nav-link <?php echo $currentPage === basename($item['url']) ? 'active' : ''; ?>">
                        <i class="bi <?php echo htmlspecialchars($item['icon']); ?>"></i>
                        <?php echo htmlspecialchars($item['title']); ?>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </nav>

        <button id="theme-toggle" class="btn">
            <i class="bi bi-moon"></i>
            <span>تغيير المظهر</span>
        </button>
    </div>

    <!-- Mobile Sidebar Toggle -->
    <button id="toggle-sidebar" class="btn btn-primary position-fixed d-lg-none" style="top: 1rem; right: 1rem; z-index: 1001;">
        <i class="bi bi-list"></i>
    </button>

    <!-- Main Content -->
    <main id="content">
        <div class="container-fluid py-4">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="card-title mb-0">نظرة عامة على قاعدة البيانات</h5>
                            </div>

                            <div class="nav-pills-wrapper mb-4">
                                <ul class="nav nav-pills">
                                    <?php foreach ($valid_tables as $table_key => $table_name): ?>
                                        <li class="nav-item">
                                            <a class="nav-link <?php echo $selected_table === $table_key ? 'active' : ''; ?>" 
                                               href="?table=<?php echo $table_key; ?>">
                                                <?php echo $table_name; ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>

                            <?php if($selected_table == 'contract'): ?>
                            <div class="table-filters">
                                <form method="GET" id="contractFilter" class="mb-0">
                                    <input type="hidden" name="table" value="contract" />
                                    
                                    <!-- Quick Filters for Contracts -->
                                    <div class="quick-filters">
                                        <div class="d-flex align-items-center gap-2 flex-wrap">
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="project_filter" class="form-label fw-bold mb-2">اختر المشروع</label>
                                                <select class="form-select select2-search" id="project_filter" name="project_filter">
                                                    <option value="">جميع المشاريع</option>
                                                    <?php 
                                                    // Get projects list for filter
                                                    $projects_query = "SELECT id_Project, Project_name FROM project WHERE Project_status = 1 ORDER BY Project_name";
                                                    $projects = $conn->query($projects_query);
                                                    
                                                    if($projects) {
                                                        while ($project = $projects->fetch_assoc()): ?>
                                                            <option value="<?php echo $project['id_Project']; ?>" <?php echo (isset($_GET['project_filter']) && $_GET['project_filter'] == $project['id_Project']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($project['Project_name']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="employee_filter" class="form-label fw-bold mb-2">اختر الموظف</label>
                                                <select class="form-select select2-search" id="employee_filter" name="employee_filter">
                                                    <option value="">جميع الموظفين</option>
                                                    <?php 
                                                    // Get employees list for filter
                                                    $employees_query = "SELECT DISTINCT e.id_employees, e.name_ar_contract 
                                                                      FROM employees e 
                                                                      WHERE e.status = 1 
                                                                      ORDER BY e.name_ar_contract";
                                                    $employees = $conn->query($employees_query);
                                                    
                                                    if($employees) {
                                                        while ($employee = $employees->fetch_assoc()): ?>
                                                            <option value="<?php echo $employee['id_employees']; ?>" <?php echo (isset($_GET['employee_filter']) && $_GET['employee_filter'] == $employee['id_employees']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($employee['name_ar_contract']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <?php if(!empty($_GET) && count($_GET) > 1): ?>
                                            <a href="?table=contract" class="btn btn-outline-secondary btn-sm">
                                                <i class="bi bi-x-circle"></i> مسح الفلاتر
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <?php if($selected_table == 'extension_contract'): ?>
                            <div class="table-filters">
                                <form method="GET" id="extensionContractFilter" class="mb-0">
                                    <input type="hidden" name="table" value="extension_contract" />
                                    
                                    <!-- Quick Filters for Extension Contracts -->
                                    <div class="quick-filters">
                                        <div class="d-flex align-items-center gap-2 flex-wrap">
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="project_filter" class="form-label fw-bold mb-2">اختر المشروع</label>
                                                <select class="form-select select2-search" id="project_filter" name="project_filter">
                                                    <option value="">جميع المشاريع</option>
                                                    <?php 
                                                    // Get projects list for filter
                                                    $projects_query = "SELECT id_Project, Project_name FROM project WHERE Project_status = 1 ORDER BY Project_name";
                                                    $projects = $conn->query($projects_query);
                                                    
                                                    if($projects) {
                                                        while ($project = $projects->fetch_assoc()): ?>
                                                            <option value="<?php echo $project['id_Project']; ?>" <?php echo (isset($_GET['project_filter']) && $_GET['project_filter'] == $project['id_Project']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($project['Project_name']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="employee_filter" class="form-label fw-bold mb-2">اختر الموظف</label>
                                                <select class="form-select select2-search" id="employee_filter" name="employee_filter">
                                                    <option value="">جميع الموظفين</option>
                                                    <?php 
                                                    // Get employees list for filter
                                                    $employees_query = "SELECT DISTINCT e.id_employees, e.name_ar_contract 
                                                                      FROM employees e 
                                                                      WHERE e.status = 1 
                                                                      ORDER BY e.name_ar_contract";
                                                    $employees = $conn->query($employees_query);
                                                    
                                                    if($employees) {
                                                        while ($employee = $employees->fetch_assoc()): ?>
                                                            <option value="<?php echo $employee['id_employees']; ?>" <?php echo (isset($_GET['employee_filter']) && $_GET['employee_filter'] == $employee['id_employees']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($employee['name_ar_contract']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <?php if(!empty($_GET) && count($_GET) > 1): ?>
                                            <a href="?table=extension_contract" class="btn btn-outline-secondary btn-sm">
                                                <i class="bi bi-x-circle"></i> مسح الفلاتر
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <?php if($selected_table == 'attendance_notes'): ?>
                            <div class="table-filters">
                                <form method="GET" id="attendanceNotesFilter" class="mb-0">
                                    <input type="hidden" name="table" value="attendance_notes" />
                                    
                                    <!-- Quick Filters for Attendance Notes -->
                                    <div class="quick-filters">
                                        <div class="d-flex align-items-center gap-2 flex-wrap">
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="project_filter" class="form-label fw-bold mb-2">اختر المشروع</label>
                                                <select class="form-select select2-search" id="project_filter" name="project_filter">
                                                    <option value="">جميع المشاريع</option>
                                                    <?php 
                                                    // Get projects list for filter
                                                    $projects_query = "SELECT id_Project, Project_name FROM project WHERE Project_status = 1 ORDER BY Project_name";
                                                    $projects = $conn->query($projects_query);
                                                    
                                                    if($projects) {
                                                        while ($project = $projects->fetch_assoc()): ?>
                                                            <option value="<?php echo $project['id_Project']; ?>" <?php echo (isset($_GET['project_filter']) && $_GET['project_filter'] == $project['id_Project']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($project['Project_name']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="employee_filter" class="form-label fw-bold mb-2">اختر الموظف</label>
                                                <select class="form-select select2-search" id="employee_filter" name="employee_filter">
                                                    <option value="">جميع الموظفين</option>
                                                    <?php 
                                                    // Get employees list for filter
                                                    $employees_query = "SELECT DISTINCT e.id_employees, e.name_ar_contract 
                                                                      FROM employees e 
                                                                      WHERE e.status = 1 
                                                                      ORDER BY e.name_ar_contract";
                                                    $employees = $conn->query($employees_query);
                                                    
                                                    if($employees) {
                                                        while ($employee = $employees->fetch_assoc()): ?>
                                                            <option value="<?php echo $employee['id_employees']; ?>" <?php echo (isset($_GET['employee_filter']) && $_GET['employee_filter'] == $employee['id_employees']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($employee['name_ar_contract']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <?php if(!empty($_GET) && count($_GET) > 1): ?>
                                            <a href="?table=attendance_notes" class="btn btn-outline-secondary btn-sm">
                                                <i class="bi bi-x-circle"></i> مسح الفلاتر
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <?php if($selected_table == 'achievement_reports'): ?>
                            <div class="table-filters">
                                <form method="GET" id="achievementReportsFilter" class="mb-0">
                                    <input type="hidden" name="table" value="achievement_reports" />
                                    
                                    <!-- Quick Filters for Achievement Reports -->
                                    <div class="quick-filters">
                                        <div class="d-flex align-items-center gap-2 flex-wrap">
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="project_filter" class="form-label fw-bold mb-2">اختر المشروع</label>
                                                <select class="form-select select2-search" id="project_filter" name="project_filter">
                                                    <option value="">جميع المشاريع</option>
                                                    <?php 
                                                    // Get projects list for filter
                                                    $projects_query = "SELECT id_Project, Project_name FROM project WHERE Project_status = 1 ORDER BY Project_name";
                                                    $projects = $conn->query($projects_query);
                                                    
                                                    if($projects) {
                                                        while ($project = $projects->fetch_assoc()): ?>
                                                            <option value="<?php echo $project['id_Project']; ?>" <?php echo (isset($_GET['project_filter']) && $_GET['project_filter'] == $project['id_Project']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($project['Project_name']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="employee_filter" class="form-label fw-bold mb-2">اختر الموظف</label>
                                                <select class="form-select select2-search" id="employee_filter" name="employee_filter">
                                                    <option value="">جميع الموظفين</option>
                                                    <?php 
                                                    // Get employees list for filter
                                                    $employees_query = "SELECT DISTINCT e.id_employees, e.name_ar_contract 
                                                                      FROM employees e 
                                                                      WHERE e.status = 1 
                                                                      ORDER BY e.name_ar_contract";
                                                    $employees = $conn->query($employees_query);
                                                    
                                                    if($employees) {
                                                        while ($employee = $employees->fetch_assoc()): ?>
                                                            <option value="<?php echo $employee['id_employees']; ?>" <?php echo (isset($_GET['employee_filter']) && $_GET['employee_filter'] == $employee['id_employees']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($employee['name_ar_contract']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <div class="d-flex align-items-end">
                                                <button type="button" id="printAchievementSummary" class="btn btn-primary" style="height: 48px;">
                                                    <i class="bi bi-printer"></i> طباعة الملخص
                                                </button>
                                            </div>
                                            <?php if(!empty($_GET) && count($_GET) > 1): ?>
                                            <a href="?table=achievement_reports" class="btn btn-outline-secondary btn-sm">
                                                <i class="bi bi-x-circle"></i> مسح الفلاتر
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <?php if($selected_table == 'merit_reports'): ?>
                            <div class="table-filters">
                                <form method="GET" id="meritReportsFilter" class="mb-0">
                                    <input type="hidden" name="table" value="merit_reports" />

                                    <!-- Quick Filters for Merit Reports -->
                                    <div class="quick-filters">
                                        <div class="d-flex align-items-center gap-2 flex-wrap">
                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="project_filter" class="form-label fw-bold mb-2">اختر المشروع</label>
                                                <select class="form-select select2-search" id="project_filter" name="project_filter">
                                                    <option value="">جميع المشاريع</option>
                                                    <?php
                                                    // Get projects list for filter
                                                    $projects_query = "SELECT id_Project, Project_name FROM project WHERE Project_status = 1 ORDER BY Project_name";
                                                    $projects = $conn->query($projects_query);

                                                    if($projects) {
                                                        while ($project = $projects->fetch_assoc()): ?>
                                                            <option value="<?php echo $project['id_Project']; ?>" <?php echo (isset($_GET['project_filter']) && $_GET['project_filter'] == $project['id_Project']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($project['Project_name']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <div class="form-group" style="min-width: 300px; margin-bottom: 0;">
                                                <label for="employee_filter" class="form-label fw-bold mb-2">اختر الموظف</label>
                                                <select class="form-select select2-search" id="employee_filter" name="employee_filter">
                                                    <option value="">جميع الموظفين</option>
                                                    <?php
                                                    // Get employees list for filter
                                                    $employees_query = "SELECT DISTINCT e.id_employees, e.name_ar_contract
                                                                      FROM employees e
                                                                      WHERE e.status = 1
                                                                      ORDER BY e.name_ar_contract";
                                                    $employees = $conn->query($employees_query);

                                                    if($employees) {
                                                        while ($employee = $employees->fetch_assoc()): ?>
                                                            <option value="<?php echo $employee['id_employees']; ?>" <?php echo (isset($_GET['employee_filter']) && $_GET['employee_filter'] == $employee['id_employees']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($employee['name_ar_contract']); ?></option>
                                                        <?php endwhile;
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <!-- Date Range Filter -->
                                            <div class="form-group" style="min-width: 200px; margin-bottom: 0; position: relative;">
                                                <label for="date_from" class="form-label fw-bold mb-2">
                                                    من تاريخ
                                                    <?php if (!empty($_GET['date_from'])): ?>
                                                        <small class="text-success">(<i class="bi bi-check-circle"></i> مفعل)</small>
                                                    <?php endif; ?>
                                                </label>
                                                <div class="input-group">
                                                    <input type="date" class="form-control" id="date_from" name="date_from"
                                                           value="<?php echo isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : ''; ?>"
                                                           style="height: 48px;">
                                                    <?php if (!empty($_GET['date_from'])): ?>
                                                        <button type="button" class="btn btn-outline-secondary clear-date-filter"
                                                                data-target="date_from" style="height: 48px;" title="مسح التاريخ">
                                                            <i class="bi bi-x"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <div class="form-group" style="min-width: 200px; margin-bottom: 0; position: relative;">
                                                <label for="date_to" class="form-label fw-bold mb-2">
                                                    إلى تاريخ
                                                    <?php if (!empty($_GET['date_to'])): ?>
                                                        <small class="text-success">(<i class="bi bi-check-circle"></i> مفعل)</small>
                                                    <?php endif; ?>
                                                </label>
                                                <div class="input-group">
                                                    <input type="date" class="form-control" id="date_to" name="date_to"
                                                           value="<?php echo isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : ''; ?>"
                                                           style="height: 48px;">
                                                    <?php if (!empty($_GET['date_to'])): ?>
                                                        <button type="button" class="btn btn-outline-secondary clear-date-filter"
                                                                data-target="date_to" style="height: 48px;" title="مسح التاريخ">
                                                            <i class="bi bi-x"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <div class="d-flex align-items-end gap-2">
                                                <button type="button" id="printMeritSummary" class="btn btn-primary" style="height: 48px;">
                                                    <i class="bi bi-printer"></i> طباعة الملخص
                                                </button>
                                            </div>
                                            <?php if(!empty($_GET) && count($_GET) > 1): ?>
                                            <a href="?table=merit_reports" class="btn btn-outline-secondary btn-sm">
                                                <i class="bi bi-x-circle"></i> مسح الفلاتر
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <?php endif; ?>

                            <?php if ($result && $result->num_rows > 0): ?>
                                <!-- Table Container -->
                                <div class="table-container">
                                    <div class="table-scroll-container">
                                        <table class="table" id="dataTable">
                                            <thead>
                                                <tr>
                                                    <?php
                                                    $first_row = $result->fetch_assoc();
                                                    $result->data_seek(0);
                                                    
                                                    foreach ($first_row as $key => $value) {
                                                        // Skip certain columns
                                                        if (in_array($key, ['data', 'data_todo_list_contract', 'data_todo_list_achievement', 'data_todo_list_Job', 
                                                            'achievement_rate', 'achievement'])) {
                                                            continue;
                                                        }
                                                        
                                                        // Get Arabic column name and column class
                                                        $column_name = isset($column_translations[$key]) ? $column_translations[$key] : $key;
                                                        $column_class = getColumnClass($key);
                                                        echo "<th class='{$column_class}'>" . htmlspecialchars($column_name) . "</th>";
                                                    }
                                                    ?>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($row = $result->fetch_assoc()): ?>
                                                    <tr>
                                                        <?php foreach ($row as $key => $value): ?>
                                                            <?php
                                                            // Skip certain columns
                                                            if (in_array($key, ['data', 'data_todo_list_contract', 'data_todo_list_achievement', 'data_todo_list_Job',
                                                                'achievement_rate', 'achievement'])) {
                                                                continue;
                                                            }
                                                            
                                                            $column_class = getColumnClass($key);
                                                            echo "<td class='{$column_class}'>" . getFormattedValue($key, $value) . "</td>";
                                                            ?>
                                                        <?php endforeach; ?>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- DataTable Controls Bottom -->
                                <div class="dataTables_wrapper dt-bootstrap5">
                                    <div class="row mt-3">
                                        <div class="col-sm-6">
                                            <div class="dataTables_info"></div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="dataTables_paginate"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    لا توجد بيانات متوفرة في هذا القسم
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="../assets/lib/datatables/jquery.dataTables.min.js"></script>
    <script src="../assets/lib/datatables/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for project and employee dropdowns
            if($('.select2-search').length > 0) {
                $('.select2-search').select2({
                    theme: 'bootstrap-5',
                    width: '100%',
                    placeholder: 'اختر أو ابحث',
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "لا توجد نتائج";
                        },
                        searching: function() {
                            return "جاري البحث...";
                        }
                    }
                }).on('change', function() {
                    $(this).closest('form').submit();
                });
            }
            
            let dataTable = $('#dataTable').DataTable({
                language: {
                    search: "بحث:",
                    lengthMenu: "عرض _MENU_ سجلات",
                    info: "عرض _START_ إلى _END_ من _TOTAL_ سجل",
                    infoEmpty: "لا توجد سجلات متاحة",
                    infoFiltered: "(تمت التصفية من _MAX_ سجل)",
                    zeroRecords: "لا توجد بيانات متوفرة في هذا القسم",
                    emptyTable: "لا توجد بيانات متوفرة في هذا القسم",
                    paginate: {
                        first: '<i class="bi bi-chevron-double-right"></i>',
                        previous: '<i class="bi bi-chevron-right"></i>',
                        next: '<i class="bi bi-chevron-left"></i>',
                        last: '<i class="bi bi-chevron-double-left"></i>'
                    }
                },
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                order: [[0, 'desc']],
                scrollX: true,
                dom: '<"row"<"col-sm-6"f><"col-sm-6"l>>' +
                     '<"row"<"col-sm-12 table-responsive"tr>>' +
                     '<"row"<"col-sm-6"i><"col-sm-6 d-flex justify-content-end"p>>',
                drawCallback: function() {
                    $('.table tbody tr').each(function() {
                        $(this).find('td').css('background-color', 'var(--bg-card)');
                    });
                    
                    $('.paginate_button').each(function() {
                        if (!$(this).hasClass('current')) {
                            $(this).css('background-color', 'var(--bg-card)');
                        }
                    });
                    
                    // Apply special formatting for merit reports table
                    const currentTable = "<?php echo $selected_table; ?>";
                    if (currentTable === 'merit_reports') {
                        $('#dataTable').addClass('merit-reports-table');
                        
                        // Reduce font sizes for merit reports
                        $('#dataTable th').css('font-size', '0.75rem');
                        $('#dataTable td').css('font-size', '0.7rem');
                        
                        // Apply currency class to monetary values
                        $('#dataTable td:contains("ريال")').each(function() {
                            $(this).addClass('currency');
                        });
                        
                        // Calculate and update the totals
                        updateMeritReportsTotals();
                        
                        // Apply column-specific classes for fixed widths and straight vertical lines
                        // First, get all headers
                        let columnHeaders = $('#dataTable thead th');
                        
                        // Then apply classes based on column content
                        // Get the column indices based on header text
                        let columnMap = {};
                        columnHeaders.each(function(index) {
                            let headerText = $(this).text().trim();
                            
                            // Map header text to CSS class
                            if (headerText.includes('رقم المشروع')) {
                                $(this).addClass('col-project');
                                columnMap['col-project'] = index;
                            } else if (headerText.includes('اسم الموظف')) {
                                $(this).addClass('col-employee');
                                columnMap['col-employee'] = index;
                            } else if (headerText.includes('المسمى الوظيفي')) {
                                $(this).addClass('col-job');
                                columnMap['col-job'] = index;
                            } else if (headerText.includes('الأجر اليومي')) {
                                $(this).addClass('col-daily-wage');
                                columnMap['col-daily-wage'] = index;
                            } else if (headerText.includes('أيام العمل')) {
                                $(this).addClass('col-working-days');
                                columnMap['col-working-days'] = index;
                            } else if (headerText.includes('إجمالي قبل الخصم')) {
                                $(this).addClass('col-total');
                                columnMap['col-total'] = index;
                            } else if (headerText.includes('نسبة الضريبة')) {
                                $(this).addClass('col-tax');
                                columnMap['col-tax'] = index;
                            } else if (headerText.includes('التأمين')) {
                                $(this).addClass('col-insurance');
                                columnMap['col-insurance'] = index;
                            } else if (headerText.includes('السلف')) {
                                $(this).addClass('col-advance');
                                columnMap['col-advance'] = index;
                            } else if (headerText.includes('إجمالي قبل الخصم')) {
                                $(this).addClass('col-total');
                                columnMap['col-total'] = index;
                            } else if (headerText.includes('الإجمالي بعد الخصم')) {
                                $(this).addClass('col-net-total');
                                columnMap['col-net-total'] = index;
                            }
                        });
                        
                        // Apply classes to table cells based on their column
                        $('#dataTable tbody tr').each(function() {
                            let cells = $(this).find('td');
                            cells.each(function(index) {
                                // Apply the classes based on the column mapping
                                if (index === columnMap['col-project']) {
                                    $(this).addClass('col-project');
                                } else if (index === columnMap['col-employee']) {
                                    $(this).addClass('col-employee');
                                } else if (index === columnMap['col-job']) {
                                    $(this).addClass('col-job');
                                } else if (index === columnMap['col-daily-wage']) {
                                    $(this).addClass('col-daily-wage');
                                } else if (index === columnMap['col-working-days']) {
                                    $(this).addClass('col-working-days');
                                } else if (index === columnMap['col-total']) {
                                    $(this).addClass('col-total');
                                } else if (index === columnMap['col-tax']) {
                                    $(this).addClass('col-tax');
                                } else if (index === columnMap['col-insurance']) {
                                    $(this).addClass('col-insurance');
                                } else if (index === columnMap['col-advance']) {
                                    $(this).addClass('col-advance');
                                } else if (index === columnMap['col-net-total']) {
                                    $(this).addClass('col-net-total');
                                }
                            });
                        });
                    }
                    else if (currentTable === 'achievement_reports') {
                        $('#dataTable').addClass('merit-reports-table');
                        
                        // Reduce font sizes for achievement reports
                        $('#dataTable th').css('font-size', '0.75rem');
                        $('#dataTable td').css('font-size', '0.7rem');
                        
                        // Apply column-specific classes for fixed widths
                        let columnHeaders = $('#dataTable thead th');
                        
                        // Map header text to CSS class
                        columnHeaders.each(function(index) {
                            let headerText = $(this).text().trim();
                            
                            if (headerText.includes('رقم التقرير')) {
                                $(this).addClass('col-report-id');
                            } else if (headerText.includes('رقم المشروع')) {
                                $(this).addClass('col-project');
                            } else if (headerText.includes('الاسم')) {
                                $(this).addClass('col-name');
                            } else if (headerText.includes('المسمى الوظيفي')) {
                                $(this).addClass('col-job');
                            } else if (headerText.includes('رقم العقد')) {
                                $(this).addClass('col-contract');
                            } else if (headerText.includes('نوع العقد')) {
                                $(this).addClass('col-type');
                            } else if (headerText.includes('رقم الهوية')) {
                                $(this).addClass('col-id');
                            } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية')) {
                                $(this).addClass('col-date');
                            } else if (headerText.includes('أيام العمل الفعلية')) {
                                $(this).addClass('col-days');
                            }
                        });
                        
                        // Apply classes to table cells and calculate working days if needed
                        $('#dataTable tbody tr').each(function() {
                            let cells = $(this).find('td');
                            cells.each(function(index) {
                                let headerText = $('#dataTable thead th').eq(index).text().trim();
                                
                                if (headerText.includes('رقم التقرير')) {
                                    $(this).addClass('col-report-id');
                                } else if (headerText.includes('رقم المشروع')) {
                                    $(this).addClass('col-project');
                                } else if (headerText.includes('الاسم')) {
                                    $(this).addClass('col-name');
                                } else if (headerText.includes('المسمى الوظيفي')) {
                                    $(this).addClass('col-job');
                                } else if (headerText.includes('رقم العقد')) {
                                    $(this).addClass('col-contract');
                                } else if (headerText.includes('نوع العقد')) {
                                    $(this).addClass('col-type');
                                } else if (headerText.includes('رقم الهوية')) {
                                    $(this).addClass('col-id');
                                } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية')) {
                                    $(this).addClass('col-date');
                                } else if (headerText.includes('أيام العمل الفعلية')) {
                                    $(this).addClass('col-days');
                                    
                                    // Calculate working days if the value is 0
                                    let workingDays = parseInt($(this).text().trim());
                                    if (workingDays === 0) {
                                        let startDate = $(this).closest('tr').find('td.col-date:first').text().trim();
                                        let endDate = $(this).closest('tr').find('td.col-date:last').text().trim();
                                        
                                        if (startDate && endDate) {
                                            let start = new Date(startDate);
                                            let end = new Date(endDate);
                                            
                                            // Calculate the difference in days
                                            let diffTime = Math.abs(end - start);
                                            // Add 1 to include both start and end dates
                                            let diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
                                            
                                            $(this).text(diffDays);
                                        }
                                    }
                                }
                            });
                        });
                    }
                    else if (currentTable === 'attendance_notes') {
                        $('#dataTable').addClass('merit-reports-table');
                        
                        // Reduce font sizes for attendance notes
                        $('#dataTable th').css('font-size', '0.75rem');
                        $('#dataTable td').css('font-size', '0.7rem');
                        
                        // Apply column-specific classes for fixed widths
                        let columnHeaders = $('#dataTable thead th');
                        
                        // Map header text to CSS class
                        columnHeaders.each(function(index) {
                            let headerText = $(this).text().trim();
                            
                            if (headerText.includes('رقم المذكرة')) {
                                $(this).addClass('col-report-id');
                            } else if (headerText.includes('رقم المشروع')) {
                                $(this).addClass('col-project');
                            } else if (headerText.includes('الاسم')) {
                                $(this).addClass('col-name');
                            } else if (headerText.includes('المسمى الوظيفي')) {
                                $(this).addClass('col-job');
                            } else if (headerText.includes('رقم العقد')) {
                                $(this).addClass('col-contract');
                            } else if (headerText.includes('نوع العقد')) {
                                $(this).addClass('col-type');
                            } else if (headerText.includes('رقم الهوية')) {
                                $(this).addClass('col-id');
                            } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية')) {
                                $(this).addClass('col-date');
                            } else if (headerText.includes('أيام العمل الفعلية')) {
                                $(this).addClass('col-days');
                            }
                        });
                        
                        // Apply classes to table cells and calculate working days if needed
                        $('#dataTable tbody tr').each(function() {
                            let cells = $(this).find('td');
                            cells.each(function(index) {
                                let headerText = $('#dataTable thead th').eq(index).text().trim();
                                
                                if (headerText.includes('رقم المذكرة')) {
                                    $(this).addClass('col-report-id');
                                } else if (headerText.includes('رقم المشروع')) {
                                    $(this).addClass('col-project');
                                } else if (headerText.includes('الاسم')) {
                                    $(this).addClass('col-name');
                                } else if (headerText.includes('المسمى الوظيفي')) {
                                    $(this).addClass('col-job');
                                } else if (headerText.includes('رقم العقد')) {
                                    $(this).addClass('col-contract');
                                } else if (headerText.includes('نوع العقد')) {
                                    $(this).addClass('col-type');
                                } else if (headerText.includes('رقم الهوية')) {
                                    $(this).addClass('col-id');
                                } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية')) {
                                    $(this).addClass('col-date');
                                } else if (headerText.includes('أيام العمل الفعلية')) {
                                    $(this).addClass('col-days');
                                    
                                    // Calculate working days if the value is 0
                                    let workingDays = parseInt($(this).text().trim());
                                    if (workingDays === 0) {
                                        let startDate = $(this).closest('tr').find('td.col-date:first').text().trim();
                                        let endDate = $(this).closest('tr').find('td.col-date:last').text().trim();
                                        
                                        if (startDate && endDate) {
                                            let start = new Date(startDate);
                                            let end = new Date(endDate);
                                            
                                            // Calculate the difference in days
                                            let diffTime = Math.abs(end - start);
                                            // Add 1 to include both start and end dates
                                            let diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
                                            
                                            $(this).text(diffDays);
                                        }
                                    }
                                }
                            });
                        });
                    }
                    else if (currentTable === 'extension_contract') {
                        $('#dataTable').addClass('merit-reports-table');
                        
                        // Reduce font sizes for extension contract table
                        $('#dataTable th').css('font-size', '0.7rem');
                        $('#dataTable td').css('font-size', '0.65rem');
                        
                        // Apply column-specific classes for fixed widths
                        let columnHeaders = $('#dataTable thead th');
                        
                        // Map header text to CSS class
                        columnHeaders.each(function(index) {
                            let headerText = $(this).text().trim();
                            
                            if (headerText.includes('رقم التمديد')) {
                                $(this).addClass('col-extension-id');
                            } else if (headerText.includes('رقم المشروع')) {
                                $(this).addClass('col-project');
                            } else if (headerText.includes('الاسم')) {
                                $(this).addClass('col-name');
                            } else if (headerText.includes('المسمى الوظيفي')) {
                                $(this).addClass('col-job');
                            } else if (headerText.includes('رقم العقد')) {
                                $(this).addClass('col-contract');
                            } else if (headerText.includes('نوع العقد')) {
                                $(this).addClass('col-type');
                            } else if (headerText.includes('رقم الهوية')) {
                                $(this).addClass('col-id');
                            } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية') || headerText.includes('تاريخ الإضافة')) {
                                $(this).addClass('col-date');
                            }
                        });
                        
                        // Apply classes to table cells
                        $('#dataTable tbody tr').each(function() {
                            let cells = $(this).find('td');
                            cells.each(function(index) {
                                let headerText = $('#dataTable thead th').eq(index).text().trim();
                                
                                if (headerText.includes('رقم التمديد')) {
                                    $(this).addClass('col-extension-id');
                                } else if (headerText.includes('رقم المشروع')) {
                                    $(this).addClass('col-project');
                                } else if (headerText.includes('الاسم')) {
                                    $(this).addClass('col-name');
                                } else if (headerText.includes('المسمى الوظيفي')) {
                                    $(this).addClass('col-job');
                                } else if (headerText.includes('رقم العقد')) {
                                    $(this).addClass('col-contract');
                                } else if (headerText.includes('نوع العقد')) {
                                    $(this).addClass('col-type');
                                } else if (headerText.includes('رقم الهوية')) {
                                    $(this).addClass('col-id');
                                } else if (headerText.includes('تاريخ البداية') || headerText.includes('تاريخ النهاية') || headerText.includes('تاريخ الإضافة')) {
                                    $(this).addClass('col-date');
                                }
                            });
                        });
                    }
                    else {
                        // Apply smaller font size to headers in all tables for consistency
                        $('#dataTable th').css('font-size', '0.75rem');
                        $('#dataTable td').css('font-size', '0.7rem');
                    }
                }
            });

            // Add event listeners for merit reports table if that's the current view
            if ("<?php echo $selected_table; ?>" === 'merit_reports') {
                // Update totals whenever table is filtered, searched, or paged
                dataTable.on('draw.dt', function() {
                    updateMeritReportsTotals();
                });
                
                // Also listen for length change events
                dataTable.on('length.dt', function() {
                    updateMeritReportsTotals();
                });
                
                // And page change events
                dataTable.on('page.dt', function() {
                    updateMeritReportsTotals();
                });
                
                // And search events
                dataTable.on('search.dt', function() {
                    updateMeritReportsTotals();
                });
            }
            
            // Handle print button click
            $('#printMeritSummary').on('click', function() {
                // Get all visible rows data from the merit reports table
                let tableData = [];
                let projectName = '';
                let projectFilter = '';
                let employeeFilter = '';
                let employeeName = 'كل الموظفين';
                
                // Get project name and filter value if selected
                if ($('#project_filter').val()) {
                    projectFilter = $('#project_filter').val();
                    projectName = $('#project_filter option:selected').text();
                }
                
                // Get current employee filter value if any
                if ($('#employee_filter').val()) {
                    employeeFilter = $('#employee_filter').val();
                    employeeName = $('#employee_filter option:selected').text();
                }
                
                // Get all visible rows (respecting current filters)
                $('#dataTable tbody tr:visible').each(function() {
                    // Skip the summary row
                    if (!$(this).hasClass('table-summary-row')) {
                        let row = {};
                        let cells = $(this).find('td');
                        
                        // Get column indices based on headers
                        let headers = [];
                        $('#dataTable thead th').each(function(index) {
                            headers[index] = $(this).text().trim();
                        });
                        
                        // Map column data to row object
                        cells.each(function(index) {
                            if (headers[index] === 'رقم المشروع') {
                                row['id_Project'] = $(this).text().trim();
                            } else if (headers[index] === 'اسم الموظف') {
                                row['name_ar_contract'] = $(this).text().trim();
                            } else if (headers[index] === 'المسمى الوظيفي') {
                                row['name_Job'] = $(this).text().trim();
                            } else if (headers[index] === 'الأجر اليومي') {
                                // Remove ريال and commas, convert to number
                                row['today_wage'] = parseFloat($(this).text().replace('ريال', '').replace(/,/g, '').trim());
                            } else if (headers[index] === 'أيام العمل الفعلية') {
                                row['actual_working_days'] = parseFloat($(this).text().trim());
                            } else if (headers[index] === 'نسبة الضريبة') {
                                row['tax_rate'] = parseFloat($(this).text().trim());
                            } else if (headers[index] === 'التأمين') {
                                row['Insurance'] = parseFloat($(this).text().replace('ريال', '').replace(/,/g, '').trim());
                            } else if (headers[index] === 'السلف') {
                                row['predecessor'] = parseFloat($(this).text().replace('ريال', '').replace(/,/g, '').trim());
                            } else if (headers[index] === 'إجمالي قبل الخصم') {
                                row['total'] = parseFloat($(this).text().replace('ريال', '').replace(/,/g, '').trim());
                            } else if (headers[index] === 'الإجمالي بعد الخصم') {
                                row['total_after_discount'] = parseFloat($(this).text().replace('ريال', '').replace(/,/g, '').trim());
                            }
                        });
                        
                        tableData.push(row);
                    }
                });
                
                // Prepare data for AJAX
                let printData = {
                    rows: tableData,
                    project_filter: projectFilter,
                    project_name: projectName,
                    employee_filter: employeeFilter,
                    employee_name: employeeName
                };
                
                // Create a form and submit it to the print page
                let form = $('<form>', {
                    'method': 'post',
                    'action': 'print/print_merit_summary.php',
                    'target': '_blank'
                });
                
                // Add data input
                $('<input>').attr({
                    'type': 'hidden',
                    'name': 'data',
                    'value': JSON.stringify(printData)
                }).appendTo(form);
                
                // Append form to body, submit it, and remove it
                form.appendTo('body').submit().remove();
            });

            // Handle print button click for Achievement Reports
            $('#printAchievementSummary').on('click', function() {
                // Get all visible rows data from the achievement reports table
                let tableData = [];
                let projectName = '';
                let projectFilter = '';
                let employeeFilter = '';
                let employeeName = 'كل الموظفين';
                
                // Get project name and filter value if selected
                if ($('#project_filter').val()) {
                    projectFilter = $('#project_filter').val();
                    projectName = $('#project_filter option:selected').text();
                }
                
                // Get current employee filter value if any
                if ($('#employee_filter').val()) {
                    employeeFilter = $('#employee_filter').val();
                    employeeName = $('#employee_filter option:selected').text();
                }
                
                // Get all visible rows (respecting current filters)
                $('#dataTable tbody tr:visible').each(function() {
                    // Skip the summary row
                    if (!$(this).hasClass('table-summary-row')) {
                        let row = {};
                        let cells = $(this).find('td');
                        
                        // Get column indices based on headers
                        let headers = [];
                        $('#dataTable thead th').each(function(index) {
                            headers[index] = $(this).text().trim();
                        });
                        
                        // Map column data to row object
                        cells.each(function(index) {
                            if (headers[index] === 'رقم المشروع') {
                                row['id_Project'] = $(this).text().trim();
                            } else if (headers[index] === 'الاسم') {
                                row['name_ar_contract'] = $(this).text().trim();
                            } else if (headers[index] === 'المسمى الوظيفي') {
                                row['name_Job'] = $(this).text().trim();
                            } else if (headers[index] === 'رقم الهوية') {
                                row['Identity_number_contract'] = $(this).text().trim();
                            } else if (headers[index] === 'تاريخ البداية') {
                                row['start_date_achievement_reports'] = $(this).text().trim();
                            } else if (headers[index] === 'تاريخ النهاية') {
                                row['end_date_achievement_reports'] = $(this).text().trim();
                            } else if (headers[index] === 'أيام العمل الفعلية') {
                                row['actual_working_days'] = parseInt($(this).text().trim());
                            }
                        });
                        
                        tableData.push(row);
                    }
                });
                
                // Prepare data for AJAX
                let printData = {
                    rows: tableData,
                    project_filter: projectFilter,
                    project_name: projectName,
                    employee_filter: employeeFilter,
                    employee_name: employeeName
                };
                
                // Create a form and submit it to the print page
                let form = $('<form>', {
                    'method': 'post',
                    'action': 'print/print_achievement_summary.php',
                    'target': '_blank'
                });
                
                // Add data input
                $('<input>').attr({
                    'type': 'hidden',
                    'name': 'data',
                    'value': JSON.stringify(printData)
                }).appendTo(form);
                
                // Append form to body, submit it, and remove it
                form.appendTo('body').submit().remove();
            });
        });
        
        // Function to update merit reports totals
        function updateMeritReportsTotals() {
            // Only run for merit reports table
            if ("<?php echo $selected_table; ?>" !== 'merit_reports') return;
            
            let totalBeforeDiscount = 0;
            let totalAfterDiscount = 0;
            
            // Find the indices of the total columns
            let totalColumnIndex = -1;
            let netTotalColumnIndex = -1;
            
            $('#dataTable thead th').each(function(index) {
                let headerText = $(this).text().trim();
                if (headerText === 'إجمالي قبل الخصم') {
                    totalColumnIndex = index;
                } else if (headerText === 'الإجمالي بعد الخصم') {
                    netTotalColumnIndex = index;
                }
            });
            
            // Calculate totals from visible rows (respects filtering)
            if (totalColumnIndex >= 0 && netTotalColumnIndex >= 0) {
                // Remove any existing summary row before recalculating
                $('.table-summary-row').remove();
                
                // Only count rows that are visible after DataTables filtering
                $('#dataTable tbody tr:visible').each(function() {
                    let cells = $(this).find('td');
                    
                    // Extract total amounts (removing "ريال" and commas)
                    if (totalColumnIndex < cells.length) {
                        let totalText = $(cells[totalColumnIndex]).text().replace('ريال', '').replace(/,/g, '').trim();
                        totalBeforeDiscount += parseFloat(totalText) || 0;
                    }
                    
                    if (netTotalColumnIndex < cells.length) {
                        let netTotalText = $(cells[netTotalColumnIndex]).text().replace('ريال', '').replace(/,/g, '').trim();
                        totalAfterDiscount += parseFloat(netTotalText) || 0;
                    }
                });
                
                // Format totals with English numbers and thousands separator
                totalBeforeDiscount = totalBeforeDiscount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                totalAfterDiscount = totalAfterDiscount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                
                // Create footer row
                let footerRow = '<tr class="table-summary-row"><td colspan="' + totalColumnIndex + '" class="text-start fw-bold">المجموع الكلي</td>';
                footerRow += '<td class="col-total fw-bold">' + totalBeforeDiscount + '</td>';
                
                // If there are columns between total and net total
                if (netTotalColumnIndex - totalColumnIndex > 1) {
                    footerRow += '<td colspan="' + (netTotalColumnIndex - totalColumnIndex - 1) + '"></td>';
                }
                
                footerRow += '<td class="col-net-total fw-bold">' + totalAfterDiscount + '</td></tr>';
                
                // Append footer row to table
                $('#dataTable tbody').append(footerRow);
                
                // Style the summary row
                $('.table-summary-row').css({
                    'background-color': 'var(--bg-main)',
                    'border-top': '2px solid var(--primary-color)',
                    'font-weight': 'bold'
                });
                
                $('.table-summary-row td').css('background-color', 'var(--bg-main) !important');
            }
        }
        
        document.addEventListener('themeChanged', function() {
            $('.table tbody tr').each(function() {
                $(this).find('td').css('background-color', 'var(--bg-card)');
            });

            $('.paginate_button').each(function() {
                if (!$(this).hasClass('current')) {
                    $(this).css('background-color', 'var(--bg-card)');
                }
            });
        });

        // Date filter functionality for merit reports
        $(document).ready(function() {
            // Auto-submit form when date values change (for merit reports only)
            if ("<?php echo $selected_table; ?>" === 'merit_reports') {
                $('#date_from, #date_to').on('change', function() {
                    // Add a small delay to allow user to set both dates if needed
                    setTimeout(function() {
                        $('#meritReportsFilter').submit();
                    }, 500);
                });

                // Validate date range
                $('#date_from, #date_to').on('change', function() {
                    var dateFrom = $('#date_from').val();
                    var dateTo = $('#date_to').val();

                    if (dateFrom && dateTo && dateFrom > dateTo) {
                        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                        $(this).val('');
                    }
                });

                // Clear individual date filters
                $(document).on('click', '.clear-date-filter', function() {
                    var target = $(this).data('target');
                    $('#' + target).val('');
                    $('#meritReportsFilter').submit();
                });
            }
        });
    </script>
</body>
</html>


