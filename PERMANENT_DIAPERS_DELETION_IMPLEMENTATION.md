# Permanent Diapers (Attendance Records) Deletion Implementation

## Overview
This document describes the implementation of automatic deletion of attendance records (`permanent_diapers` table) when contract types change between open/closed states during contract updates.

## Implementation Details

### 1. Database Logic Enhancement

#### Modified Contract Update Query
- Enhanced the contract retrieval query to include `end_date_contract` field
- Added logic to determine the current contract type in the 4-value system (1-4)

#### Contract Type Mapping
- **1**: Monthly Open (contract_type=1, end_date_contract=NULL)
- **2**: Monthly Closed (contract_type=1, end_date_contract!=NULL)
- **3**: Daily Open (contract_type=2, end_date_contract=NULL)
- **4**: Daily Closed (contract_type=2, end_date_contract!=NULL)

#### Open/Closed State Detection
```php
// Determine current contract type in 4-value system
$current_selected_contract_type = 0;
if ($current_contract_type == 1) { // Monthly
    if ($current_contract['end_date_contract']) {
        $current_selected_contract_type = 2; // Monthly Closed
    } else {
        $current_selected_contract_type = 1; // Monthly Open
    }
} else { // Daily
    if ($current_contract['end_date_contract']) {
        $current_selected_contract_type = 4; // Daily Closed
    } else {
        $current_selected_contract_type = 3; // Daily Open
    }
}

// Check if there's a change between open/closed states
$current_is_open = ($current_selected_contract_type == 1 || $current_selected_contract_type == 3);
$new_is_open = ($selected_contract_type == 1 || $selected_contract_type == 3);
```

#### Deletion Logic
When open/closed state changes are detected:
```php
if ($current_is_open != $new_is_open) {
    // Delete all related permanent_diapers (attendance records)
    $delete_diapers_sql = "DELETE FROM permanent_diapers WHERE id_contract = ?";
    $delete_diapers_stmt = $conn->prepare($delete_diapers_sql);
    // ... [execution logic]
}
```

### 2. Deletion Conditions
The permanent_diapers deletion occurs in these specific cases:
- **Monthly Closed → Monthly Open** (2 → 1)
- **Monthly Closed → Daily Open** (2 → 3)
- **Monthly Open → Monthly Closed** (1 → 2)
- **Monthly Open → Daily Closed** (1 → 4)
- **Daily Open → Monthly Closed** (3 → 2)
- **Daily Open → Daily Closed** (3 → 4)
- **Daily Closed → Monthly Open** (4 → 1)
- **Daily Closed → Daily Open** (4 → 3)

### 3. Enhanced Success Messages
The system now provides detailed feedback about what was deleted:

```php
if ($achievement_reports_deleted && $permanent_diapers_deleted) {
    $success_message .= ". تم حذف تقارير الإنجاز وسجلات الحضور المرتبطة بالعقد بسبب تغيير نوع العقد.";
} elseif ($achievement_reports_deleted) {
    $success_message .= ". تم حذف تقارير الإنجاز المرتبطة بالعقد بسبب تغيير نوع العقد.";
} elseif ($permanent_diapers_deleted) {
    $success_message .= ". تم حذف سجلات الحضور المرتبطة بالعقد بسبب تغيير حالة العقد من مفتوح إلى مغلق أو العكس.";
}
```

### 4. Enhanced Warning Dialog
The JavaScript validation now shows additional warnings when open/closed state changes:

```javascript
// Check if there's an open/closed state change
const originalIsOpen = (originalType === 1 || originalType === 3);
const currentIsOpen = (currentContractType === 1 || currentContractType === 3);

let confirmMessage = 'تحذير: سيؤدي تغيير نوع العقد إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.';

// Add additional warning if open/closed state is changing
if (originalIsOpen !== currentIsOpen) {
    confirmMessage += '\n\nتحذير إضافي: سيؤدي تغيير العقد من مفتوح إلى مغلق أو العكس إلى حذف جميع سجلات الحضور المرتبطة بهذا العقد أيضاً.';
}
```

## Database Tables Affected

### permanent_diapers Table Structure
- **id_permanent_diapers** (int, AI, PK)
- **id_Project** (int)
- **id_contract** (int) - Foreign key used for deletion
- **id_extension_contract** (int)
- **start_date_permanent_diapers** (datetime)
- **end_date_permanent_diapers** (datetime)
- **data** (json)
- **add_permanent_diapers** (timestamp)

## Error Handling
- Proper exception handling for database operations
- Detailed error logging for debugging purposes
- Transaction safety maintained through prepared statements

## User Experience
- Clear warning messages in Arabic
- Confirmation dialog before destructive operations
- Detailed success messages explaining what was deleted
- Separate warnings for achievement_reports vs permanent_diapers deletions

## Testing Considerations
- Test all 8 open/closed state change combinations
- Verify that only relevant records are deleted
- Confirm proper error handling for database failures
- Test user cancellation of confirmation dialogs
- Verify success messages display correctly for different scenarios
